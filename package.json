{"name": "Winelikes", "version": "9.6.0", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "a": "react-native run-android", "i": "react-native run-ios", "apk": "react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res", "debug": "cd android && ./gradlew assembleDebug && cd ..", "android-release": "react-native run-android --variant release", "build-a": "cd android && ./gradlew assembleRelease --console plain", "clean": "cd android && ./gradlew clean", "ios-se": "react-native run-ios --simulator=\"iPhone SE (2nd generation)\"", "start": "react-native start", "start-reset": "react-native start --reset-cache", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "adb": "adb reverse tcp:8081 tcp:8081", "ipod": "cd ios && pod install", "ipod-update": "cd ios && pod install --repo-update", "ios_dev": "react-native run-ios --scheme \"WineLikes_dev\" --simulator \"iPhone 15 Pro\"", "ios_prod": "react-native run-ios --scheme \"WineLikes\"", "android_dev": "cd android && ./gradlew app:installDevDebug -PreactNativeDevServerPort=8081 && adb reverse tcp:8081 tcp:8081", "android:dev:apk": "cd android && ./gradlew assembleDevRelease && cd ..", "android_prod": "cd android && ./gradlew app:installProdDebug -PreactNativeDevServerPort=8081 && adb reverse tcp:8081 tcp:8081", "android:prod:release": "react-native run-android --variant=prodrelease", "android:prod:apk": "cd android && ./gradlew assembleProdRelease && cd ..", "android:prod:aab": "cd android && ./gradlew bundleProdRelease && cd ..", "postinstall": "npx patch-package"}, "dependencies": {"@amplitude/analytics-react-native": "^1.4.13", "@gorhom/bottom-sheet": "4.6.4", "@invertase/react-native-apple-authentication": "^2.2.2", "@ptomasroos/react-native-multi-slider": "^2.2.2", "@pusher/pusher-websocket-react-native": "^1.1.1", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-camera-roll/camera-roll": "^7.10.0", "@react-native-clipboard/clipboard": "^1.11.1", "@react-native-community/datetimepicker": "^8.4.1", "@react-native-community/push-notification-ios": "^1.10.1", "@react-native-community/slider": "^4.5.0", "@react-native-documents/picker": "^10.1.3", "@react-native-firebase/app": "^22.2.0", "@react-native-firebase/auth": "^22.2.0", "@react-native-firebase/firestore": "^22.2.0", "@react-native-firebase/messaging": "^22.2.0", "@react-native-google-signin/google-signin": "latest", "@react-navigation/bottom-tabs": "^6.0.9", "@react-navigation/native": "^6.0.6", "@react-navigation/stack": "6.0.7", "@shopify/react-native-skia": "latest", "@stripe/stripe-react-native": "^0.38.6", "@types/react-native-fbsdk": "^3.0.2", "@types/react-native-version-check": "^3.4.8", "axios": "^0.26.1", "babel-plugin-module-resolver": "^4.1.0", "fbjs": "^3.0.4", "formik": "^2.2.9", "jetifier": "^2.0.0", "jwt-decode": "^3.1.2", "lodash": "^4.17.21", "lodash.debounce": "^4.0.8", "lottie-react-native": "^7.2.2", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "npm": "^9.6.5", "parse-address": "^1.1.2", "patch-package": "^8.0.0", "pusher": "^5.1.2", "react": "19.0.0", "react-native": "0.79.2", "react-native-audio-session": "^0.0.6", "react-native-autocomplete-dropdown": "^2.0.6", "react-native-bootsplash": "^5.0.2", "react-native-clarity": "^4.3.0", "react-native-compressor": "^1.8.25", "react-native-config": "^1.5.1", "react-native-device-info": "^10.11.0", "react-native-dropdown-picker": "^5.3.0", "react-native-exception-handler": "^2.10.10", "react-native-fast-image": "^8.6.3", "react-native-fbads": "^7.1.1", "react-native-fbsdk-next": "13.0.0", "react-native-flash-message": "^0.3.1", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.26.0", "react-native-google-places-autocomplete": "2.5.6", "react-native-image-crop-picker": "^0.37.3", "react-native-image-picker": "^7.1.2", "react-native-iphone-x-helper": "^1.3.1", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-localize": "^3.1.0", "react-native-modal": "^13.0.1", "react-native-modal-datetime-picker": "^17.1.0", "react-native-month-year-picker": "^1.9.0", "react-native-pager-view": "^6.8.1", "react-native-permissions": "^3.9.2", "react-native-pinchable": "^0.2.1", "react-native-push-notification": "^8.1.1", "react-native-ratings": "^8.1.0", "react-native-reanimated": "^3.18.0", "react-native-responsive-screen": "^1.4.2", "react-native-safe-area-context": "^5.4.1", "react-native-safe-area-view": "^1.1.1", "react-native-screens": "^4.11.1", "react-native-search-filter": "^0.1.5", "react-native-sensitive-info": "^5.5.8", "react-native-share": "^12.0.11", "react-native-super-grid": "^4.2.0", "react-native-svg": "^15.12.0", "react-native-svg-transformer": "^1.0.0", "react-native-tab-view": "^3.1.1", "react-native-vector-icons": "^10.0.0", "react-native-version-check": "^3.4.7", "react-native-video": "^6.15.0", "react-native-video-player": "^0.14.0", "react-native-view-shot": "^4.0.3", "react-native-webview": "^11.17.2", "redux-logger": "^3.0.6", "toggle-switch-react-native": "^3.3.0", "uuid": "^10.0.0", "yup": "^0.32.11", "zustand": "^3.6.7"}, "devDependencies": {"@babel/core": "7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "18.0.0", "@react-native-community/cli-platform-android": "18.0.0", "@react-native-community/cli-platform-ios": "18.0.0", "@react-native/babel-preset": "0.79.2", "@react-native/eslint-config": "0.79.2", "@react-native/metro-config": "0.79.2", "@react-native/typescript-config": "0.79.2", "@types/jest": "^29.5.13", "@types/lodash.debounce": "^4.0.9", "@types/react-native-vector-icons": "^6.4.10", "@types/react-native-video": "^5.0.13", "@types/react-native-video-player": "^0.10.6", "@types/react-test-renderer": "^19.0.0", "babel-plugin-module-resolver": "^4.1.0", "babel-plugin-transform-remove-console": "^6.9.4", "eslint": "^7.32.0", "eslint-plugin-prettier": "^4.2.1", "jest": "^26.6.3", "metro-react-native-babel-preset": "0.72.3", "metro-react-native-babel-transformer": "^0.77.0", "prettier": "2.7.1", "prettier-plugin-organize-imports": "^2.3.4", "react-devtools": "^4.24.7", "react-native-svg-transformer": "^0.14.3", "react-test-renderer": "19.0.0", "typescript": "5.0.4"}, "resolutions": {"@types/react": "^17"}, "jest": {"preset": "react-native", "moduleFileExtensions": ["ts", "tsx", "js", "jsx", "json", "node"]}}